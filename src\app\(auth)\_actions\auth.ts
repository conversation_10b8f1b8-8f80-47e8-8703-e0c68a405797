'use server'

import { z } from 'zod'
import bcrypt from 'bcryptjs'
import { redirect } from 'next/navigation'
import { revalidateTag } from 'next/cache'
import { signUpSchema, signInSchema } from '@/lib/validations/auth'
import type { APIResponse } from '@/lib/types'

// Mock database operations (replace with actual Prisma calls)
const mockUsers: Array<{
  id: string
  email: string
  name: string
  passwordHash: string
  role: string
  organizationId?: string
  createdAt: Date
}> = []

const mockOrganizations: Array<{
  id: string
  name: string
  plan: string
  createdAt: Date
}> = []

// Helper function to generate IDs (replace with proper UUID generation)
const generateId = () => Math.random().toString(36).substring(2, 15)

// Helper function to create session (replace with actual session management)
const createSession = async (userId: string) => {
  // This would typically set a secure HTTP-only cookie
  // For now, we'll just simulate the process
  console.log(`Creating session for user: ${userId}`)
  return { sessionId: generateId(), userId }
}

export async function signUpAction(formData: FormData): Promise<APIResponse> {
  try {
    // Extract and validate form data
    const rawData = {
      name: formData.get('name'),
      email: formData.get('email'),
      password: formData.get('password'),
      confirmPassword: formData.get('confirmPassword'),
      role: formData.get('role'),
      organizationName: formData.get('organizationName'),
      acceptTerms: formData.get('acceptTerms') === 'on',
      marketingEmails: formData.get('marketingEmails') === 'on',
    }

    const validatedData = signUpSchema.parse(rawData)

    // Check if user already exists
    const existingUser = mockUsers.find(user => user.email === validatedData.email)
    if (existingUser) {
      return {
        error: 'An account with this email already exists. Please sign in instead.',
      }
    }

    // Hash password
    const passwordHash = await bcrypt.hash(validatedData.password, 12)

    // Create organization if needed
    let organizationId: string | undefined
    if (validatedData.organizationName && ['broker', 'developer'].includes(validatedData.role)) {
      const organization = {
        id: generateId(),
        name: validatedData.organizationName,
        plan: 'free' as const,
        createdAt: new Date(),
      }
      mockOrganizations.push(organization)
      organizationId = organization.id
    }

    // Create user
    const user = {
      id: generateId(),
      email: validatedData.email,
      name: validatedData.name,
      passwordHash,
      role: validatedData.role,
      organizationId,
      createdAt: new Date(),
    }
    mockUsers.push(user)

    // Create session
    await createSession(user.id)

    // Revalidate relevant cache tags
    revalidateTag('users')
    revalidateTag('organizations')

    // Return success response
    return {
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          organizationId: user.organizationId,
        },
      },
      message: 'Account created successfully! Welcome to RealHub.',
    }
  } catch (error) {
    console.error('Sign up error:', error)

    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      return {
        error: firstError?.message || 'Please check your input and try again.',
      }
    }

    return {
      error: 'Something went wrong. Please try again later.',
    }
  }
}

export async function signInAction(formData: FormData): Promise<APIResponse> {
  try {
    // Extract and validate form data
    const rawData = {
      email: formData.get('email'),
      password: formData.get('password'),
      rememberMe: formData.get('rememberMe') === 'on',
    }

    const validatedData = signInSchema.parse(rawData)

    // Find user by email
    const user = mockUsers.find(u => u.email === validatedData.email)
    if (!user) {
      return {
        error: 'Invalid email or password. Please check your credentials and try again.',
      }
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(validatedData.password, user.passwordHash)
    if (!isValidPassword) {
      return {
        error: 'Invalid email or password. Please check your credentials and try again.',
      }
    }

    // Create session
    await createSession(user.id)

    // Return success response
    return {
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          organizationId: user.organizationId,
        },
      },
      message: 'Welcome back! You have been signed in successfully.',
    }
  } catch (error) {
    console.error('Sign in error:', error)

    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      return {
        error: firstError?.message || 'Please check your input and try again.',
      }
    }

    return {
      error: 'Something went wrong. Please try again later.',
    }
  }
}

export async function signOutAction(): Promise<APIResponse> {
  try {
    // Clear session (replace with actual session clearing)
    console.log('Clearing user session')

    // Revalidate cache
    revalidateTag('session')

    return {
      message: 'You have been signed out successfully.',
    }
  } catch (error) {
    console.error('Sign out error:', error)
    return {
      error: 'Something went wrong while signing out.',
    }
  }
}

// Action to redirect after successful authentication
export async function redirectAfterAuth(role: string) {
  // Redirect based on user role
  switch (role) {
    case 'broker':
    case 'admin':
      redirect('/dashboard/broker')
    case 'agent':
      redirect('/dashboard/agent')
    case 'developer':
      redirect('/dashboard/developer')
    case 'buyer':
      redirect('/dashboard/buyer')
    default:
      redirect('/dashboard')
  }
}

// Helper action to check if email is available
export async function checkEmailAvailability(email: string): Promise<APIResponse<{ available: boolean }>> {
  try {
    const existingUser = mockUsers.find(user => user.email === email)
    return {
      data: { available: !existingUser },
    }
  } catch (error) {
    console.error('Email check error:', error)
    return {
      error: 'Unable to check email availability.',
    }
  }
}
