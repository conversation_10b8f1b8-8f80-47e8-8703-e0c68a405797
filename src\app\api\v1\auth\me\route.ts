import { NextRequest, NextResponse } from 'next/server'
import type { APIResponse } from '@/lib/types'

// Mock database operations (replace with actual Prisma calls)
const mockUsers: Array<{
  id: string
  email: string
  name: string
  passwordHash: string
  role: string
  organizationId?: string
  createdAt: Date
}> = []

// Mock session storage (replace with actual session management)
const mockSessions: Array<{
  token: string
  userId: string
  expiresAt: Date
}> = []

export async function GET(request: NextRequest): Promise<NextResponse<APIResponse>> {
  try {
    // Get session token from cookie or Authorization header
    const sessionToken = request.cookies.get('session-token')?.value || 
                        request.headers.get('authorization')?.replace('Bearer ', '')

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Authentication required. Please sign in.' },
        { status: 401 }
      )
    }

    // Find session (in production, validate JWT or query database)
    const session = mockSessions.find(s => s.token === sessionToken && s.expiresAt > new Date())
    if (!session) {
      return NextResponse.json(
        { error: 'Invalid or expired session. Please sign in again.' },
        { status: 401 }
      )
    }

    // Find user
    const user = mockUsers.find(u => u.id === session.userId)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found. Please sign in again.' },
        { status: 401 }
      )
    }

    // Return user data (exclude sensitive information)
    const response: APIResponse = {
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          organizationId: user.organizationId,
          createdAt: user.createdAt,
        },
      },
    }

    return NextResponse.json(response, { status: 200 })
  } catch (error) {
    console.error('API Me error:', error)

    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve user information.' },
    { status: 405 }
  )
}

export async function PUT(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve user information.' },
    { status: 405 }
  )
}

export async function DELETE(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve user information.' },
    { status: 405 }
  )
}

export async function PATCH(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve user information.' },
    { status: 405 }
  )
}
