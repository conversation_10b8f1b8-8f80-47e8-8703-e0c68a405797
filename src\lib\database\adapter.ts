import type {
	DatabaseAdapter,
	DatabaseConfig,
	Repository,
	User,
	Organization,
	Listing,
	Agent,
	Lead,
	Session,
	CreateUserInput,
	UpdateUserInput,
	CreateOrganizationInput,
	UpdateOrganizationInput,
	CreateListingInput,
	UpdateListingInput,
	CreateAgentInput,
	UpdateAgentInput,
	CreateLeadInput,
	UpdateLeadInput,
	CreateSessionInput,
	UpdateSessionInput
} from '@/lib/types'

/**
 * Narrow union of supported database types so we can safely treat the
 * value coming from the environment as a discriminant.
 */
type SupportedDatabase = 'postgresql' | 'mongodb'

/**
 * Explicitly model the optional `options` bag as an index signature
 * instead of falling back to `any`.
 */
export interface StrictDatabaseConfig extends Omit<DatabaseConfig, 'options' | 'type'> {
	readonly type: SupportedDatabase
	/**
	 * Additional client‑specific options – Prisma or MongoDB.  We keep the
	 * value as `unknown` until it reaches the concrete adapter where it is
	 * safely down‑cast.
	 */
	readonly options?: Record<string, unknown>
}

/* ------------------------------------------------------------------ */
/* 3rd‑party client facades – only expose what we actually use.       */
/* ------------------------------------------------------------------ */

interface PrismaClientLike {
	$connect(): Promise<void>
	$disconnect(): Promise<void>
	$transaction<T>(fn: () => Promise<T>): Promise<T>
}

interface MongoDbLike {
	/* add collection‑level helpers here when needed */
	/* placeholder */
}

interface MongoSessionLike {
	withTransaction<T>(fn: () => Promise<T>): Promise<T>
	endSession(): Promise<void>
}

interface MongoClientLike {
	connect(): Promise<void>
	close(): Promise<void>
	startSession(): MongoSessionLike
	// eslint‑disable-next-line @typescript-eslint/no-explicit-any
	db(name: string): MongoDbLike
}

/* ------------------------------------------------------------------ */
/*              Database adapter factory & abstract base              */
/* ------------------------------------------------------------------ */

export class DatabaseAdapterFactory {
	static create(config: StrictDatabaseConfig): DatabaseAdapter {
		switch (config.type) {
			case 'postgresql':
				return new PostgreSQLAdapter(config)
			case 'mongodb':
				return new MongoDBAdapter(config)
			default: {
				// Using `never` forces the compiler to flag unsupported values.
				// eslint-disable-next-line @typescript-eslint/ban-ts-comment
				// @ts-expect-error – exhaustive switch
				const _exhaustive: never = config.type
				throw new Error(`Unsupported database type: ${_exhaustive}`)
			}
		}
	}
}

abstract class BaseAdapter implements DatabaseAdapter {
	protected connected = false

	constructor(protected readonly config: StrictDatabaseConfig) {}

	protected ensureConnected(): void {
		if (!this.connected) {
			throw new Error('Database not connected. Call connect() first.')
		}
	}

	abstract connect(): Promise<void>
	abstract disconnect(): Promise<void>
	abstract transaction<T>(fn: () => Promise<T>): Promise<T>

	/* Repositories – leave unimplemented for now */
	abstract getUserRepository(): Repository<User, CreateUserInput, UpdateUserInput>
	abstract getOrganizationRepository(): Repository<Organization, CreateOrganizationInput, UpdateOrganizationInput>
	abstract getListingRepository(): Repository<Listing, CreateListingInput, UpdateListingInput>
	abstract getAgentRepository(): Repository<Agent, CreateAgentInput, UpdateAgentInput>
	abstract getLeadRepository(): Repository<Lead, CreateLeadInput, UpdateLeadInput>
	abstract getSessionRepository(): Repository<Session, CreateSessionInput, UpdateSessionInput>
}

/* ------------------------------------------------------------------ */
/*                          PostgreSQL (Prisma)                       */
/* ------------------------------------------------------------------ */

export class PostgreSQLAdapter extends BaseAdapter {
	private prisma: PrismaClientLike | null = null

	async connect(): Promise<void> {
		try {
			const { PrismaClient } = await import('@prisma/client')
			this.prisma = new PrismaClient({
				datasources: { db: { url: this.config.connectionString } },
				// Safely spread optional options bag.
				...(this.config.options ?? {})
			}) as PrismaClientLike

			await this.prisma.$connect()
			this.connected = true
		} catch (error) {
			throw new Error(`Failed to connect to PostgreSQL: ${String(error)}`)
		}
	}

	async disconnect(): Promise<void> {
		if (!this.prisma) return
		await this.prisma.$disconnect()
		this.connected = false
	}

	async transaction<T>(fn: () => Promise<T>): Promise<T> {
		this.ensureConnected()
		if (!this.prisma) {
			throw new Error('Prisma client not initialised')
		}
		return this.prisma.$transaction(fn)
	}

	/* Repositories – implementations pending */
	getUserRepository(): Repository<User, CreateUserInput, UpdateUserInput> {
		throw new Error('PostgreSQLUserRepository not implemented yet')
	}
	getOrganizationRepository(): Repository<Organization, CreateOrganizationInput, UpdateOrganizationInput> {
		throw new Error('PostgreSQLOrganizationRepository not implemented yet')
	}
	getListingRepository(): Repository<Listing, CreateListingInput, UpdateListingInput> {
		throw new Error('PostgreSQLListingRepository not implemented yet')
	}
	getAgentRepository(): Repository<Agent, CreateAgentInput, UpdateAgentInput> {
		throw new Error('PostgreSQLAgentRepository not implemented yet')
	}
	getLeadRepository(): Repository<Lead, CreateLeadInput, UpdateLeadInput> {
		throw new Error('PostgreSQLLeadRepository not implemented yet')
	}
	getSessionRepository(): Repository<Session, CreateSessionInput, UpdateSessionInput> {
		throw new Error('PostgreSQLSessionRepository not implemented yet')
	}
}

/* ------------------------------------------------------------------ */
/*                          MongoDB (native)                          */
/* ------------------------------------------------------------------ */

export class MongoDBAdapter extends BaseAdapter {
	private client: MongoClientLike | null = null
	private db: MongoDbLike | null = null

	async connect(): Promise<void> {
		try {
			const { MongoClient } = await import('mongodb')
			this.client = new MongoClient(
				this.config.connectionString,
				(this.config.options ?? {}) as never
			) as MongoClientLike
			await this.client.connect()

			const dbName = new URL(this.config.connectionString).pathname.substring(1)
			this.db = this.client.db(dbName)

			this.connected = true
		} catch (error) {
			throw new Error(`Failed to connect to MongoDB: ${String(error)}`)
		}
	}

	async disconnect(): Promise<void> {
		if (!this.client) return
		await this.client.close()
		this.connected = false
	}

	async transaction<T>(fn: () => Promise<T>): Promise<T> {
		this.ensureConnected()
		if (!this.client) throw new Error('MongoDB client not initialised')

		const session = this.client.startSession()
		try {
			return await session.withTransaction(fn)
		} finally {
			await session.endSession()
		}
	}

	/* Repositories – implementations pending */
	getUserRepository(): Repository<User, CreateUserInput, UpdateUserInput> {
		throw new Error('MongoDBUserRepository not implemented yet')
	}
	getOrganizationRepository(): Repository<Organization, CreateOrganizationInput, UpdateOrganizationInput> {
		throw new Error('MongoDBOrganizationRepository not implemented yet')
	}
	getListingRepository(): Repository<Listing, CreateListingInput, UpdateListingInput> {
		throw new Error('MongoDBListingRepository not implemented yet')
	}
	getAgentRepository(): Repository<Agent, CreateAgentInput, UpdateAgentInput> {
		throw new Error('MongoDBAgentRepository not implemented yet')
	}
	getLeadRepository(): Repository<Lead, CreateLeadInput, UpdateLeadInput> {
		throw new Error('MongoDBLeadRepository not implemented yet')
	}
	getSessionRepository(): Repository<Session, CreateSessionInput, UpdateSessionInput> {
		throw new Error('MongoDBSessionRepository not implemented yet')
	}
}

/* ------------------------------------------------------------------ */
/*                         Singleton management                       */
/* ------------------------------------------------------------------ */

let databaseInstance: DatabaseAdapter | null = null

export function getDatabaseAdapter(): DatabaseAdapter {
	if (!databaseInstance) {
		if (!process.env.DATABASE_URL) {
			throw new Error('DATABASE_URL is not defined')
		}

		const typeEnv = (process.env.DATABASE_TYPE ?? 'postgresql') as SupportedDatabase

		databaseInstance = DatabaseAdapterFactory.create({
			type: typeEnv,
			connectionString: process.env.DATABASE_URL,
			options: undefined
		})
	}

	return databaseInstance
}

export async function initializeDatabase(): Promise<void> {
	await getDatabaseAdapter().connect()
}

export async function closeDatabase(): Promise<void> {
	await getDatabaseAdapter().disconnect()
}
