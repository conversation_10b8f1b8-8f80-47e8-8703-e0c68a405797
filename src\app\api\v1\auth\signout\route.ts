import { NextRequest, NextResponse } from 'next/server'
import type { APIResponse } from '@/lib/types'

export async function POST(request: NextRequest): Promise<NextResponse<APIResponse>> {
  try {
    // In a real implementation, you would:
    // 1. Validate the session token
    // 2. Invalidate the session in your database
    // 3. Add the token to a blacklist if using JWTs

    const response: APIResponse = {
      message: 'You have been signed out successfully.',
    }

    // Clear the session cookie
    const nextResponse = NextResponse.json(response, { status: 200 })
    nextResponse.cookies.set('session-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: new Date(0), // Expire immediately
      path: '/',
    })

    return nextResponse
  } catch (error) {
    console.error('API Signout error:', error)

    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sign out.' },
    { status: 405 }
  )
}

export async function PUT(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sign out.' },
    { status: 405 }
  )
}

export async function DELETE(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sign out.' },
    { status: 405 }
  )
}

export async function PATCH(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sign out.' },
    { status: 405 }
  )
}
