import { Metadata } from 'next'
import Link from 'next/link'
import { SignUpForm } from '../_components/signup-form'
import { Home } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Sign Up - RealHub',
  description: 'Create your RealHub account and start transforming your real estate business with AI-powered tools.',
  robots: {
    index: false,
    follow: false,
  },
}

export default function SignUpPage() {
  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-primary rounded-lg p-1">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Home className="w-5 h-5 text-text" />
              </div>
              <span className="text-xl font-bold text-text">RealHub</span>
            </Link>
            <div className="text-sm text-text-soft">
              Already have an account?{' '}
              <Link 
                href="/auth/signin" 
                className="text-primary hover:text-primary-hover font-medium focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
              >
                Sign in
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-text mb-2">
              Create your account
            </h1>
            <p className="text-text-soft">
              Join thousands of real estate professionals using RealHub to grow their business
            </p>
          </div>

          <SignUpForm />

          <div className="text-center">
            <p className="text-sm text-text-soft">
              By creating an account, you agree to our{' '}
              <Link 
                href="/terms" 
                className="text-primary hover:text-primary-hover focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
              >
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link 
                href="/privacy" 
                className="text-primary hover:text-primary-hover focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
              >
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-border bg-card/50 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <p className="text-sm text-text-soft">
              © 2024 RealHub. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <Link 
                href="/help" 
                className="text-sm text-text-soft hover:text-text focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
              >
                Help
              </Link>
              <Link 
                href="/contact" 
                className="text-sm text-text-soft hover:text-text focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
              >
                Contact
              </Link>
              <Link 
                href="/status" 
                className="text-sm text-text-soft hover:text-text focus:outline-none focus:ring-2 focus:ring-primary rounded px-1"
              >
                Status
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
