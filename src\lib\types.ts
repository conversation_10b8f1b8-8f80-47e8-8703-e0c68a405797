export interface User {
	id: string
	email: string
	name?: string
	role: UserRole
	organizationId?: string
	createdAt: Date
	updatedAt: Date
}

export type UserRole = 'owner' | 'admin' | 'agent' | 'broker' | 'developer' | 'buyer'

export interface Organization {
	id: string
	name: string
	plan: 'free' | 'pro' | 'enterprise'
	settings: OrganizationSettings
	createdAt: Date
	updatedAt: Date
}

export interface OrganizationSettings {
	allowPublicListings: boolean
	requireApproval: boolean
	defaultLanguage: 'en' | 'tl' | 'ceb'
	timezone: string
}

export interface Listing {
	id: string
	organizationId: string
	title: string
	description: string
	price: number
	beds: number
	baths: number
	sqft?: number
	address: string
	city: string
	state: string
	zipCode: string
	country: string
	latitude?: number
	longitude?: number
	status: 'draft' | 'active' | 'pending' | 'sold' | 'archived'
	type: 'house' | 'apartment' | 'condo' | 'townhouse' | 'land' | 'commercial'
	images: string[]
	features: string[]
	agentId?: string
	createdAt: Date
	updatedAt: Date
	seo?: SEOOverride
}

export interface Agent {
	id: string
	userId?: string
	name: string
	email: string
	phone?: string
	bio?: string
	avatar?: string
	licenseNumber?: string
	organizationId: string
	specialties: string[]
	languages: string[]
	isActive: boolean
	createdAt: Date
	updatedAt: Date
}

export interface Lead {
	id: string
	name: string
	email: string
	phone?: string
	message?: string
	source: 'website' | 'referral' | 'social' | 'advertising' | 'other'
	status: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost'
	listingId?: string
	agentId?: string
	organizationId: string
	aiScore?: number
	aiInsights?: string
	createdAt: Date
	updatedAt: Date
}

export interface SEOSettings {
	id: string
	organizationId: string
	siteTitle: string
	siteDescription: string
	defaultOGImage?: string
	twitterHandle?: string
	googleAnalyticsId?: string
	googleTagManagerId?: string
	facebookPixelId?: string
	robotsAllowIndexing: boolean
	canonicalBaseUrl: string
	defaultLanguage: string
	supportedLanguages: string[]
	createdAt: Date
	updatedAt: Date
}

export interface SEOOverride {
	title?: string
	description?: string
	ogImage?: string
	noIndex?: boolean
	noFollow?: boolean
	canonicalUrl?: string
}

export interface APIResponse<T = any> {
	data?: T
	error?: string
	message?: string
}

export interface PaginatedResponse<T = any> extends APIResponse<T[]> {
	pagination?: {
		page: number
		limit: number
		total: number
		totalPages: number
	}
}

export interface SearchFilters {
	query?: string
	minPrice?: number
	maxPrice?: number
	beds?: number
	baths?: number
	type?: Listing['type']
	city?: string
	state?: string
	features?: string[]
	sortBy?: 'price' | 'date' | 'beds' | 'baths' | 'sqft'
	sortOrder?: 'asc' | 'desc'
}

// Database Abstraction Types
export interface DatabaseConfig {
	type: 'postgresql' | 'mongodb'
	connectionString: string
	options?: Record<string, any>
}

export interface QueryOptions {
	limit?: number
	offset?: number
	orderBy?: Array<{ field: string; direction: 'asc' | 'desc' }>
	include?: string[]
}

export interface CreateOptions {
	include?: string[]
}

export interface UpdateOptions {
	include?: string[]
	upsert?: boolean
}

export interface DeleteOptions {
	cascade?: boolean
}

// Generic Repository Interface
export interface Repository<T, CreateInput, UpdateInput> {
	findById(id: string, options?: QueryOptions): Promise<T | null>
	findMany(filters?: Partial<T>, options?: QueryOptions): Promise<T[]>
	create(data: CreateInput, options?: CreateOptions): Promise<T>
	update(id: string, data: Partial<UpdateInput>, options?: UpdateOptions): Promise<T>
	delete(id: string, options?: DeleteOptions): Promise<boolean>
	count(filters?: Partial<T>): Promise<number>
}

// Database Adapter Interface
export interface DatabaseAdapter {
	connect(): Promise<void>
	disconnect(): Promise<void>
	transaction<T>(fn: () => Promise<T>): Promise<T>

	// Repository factories
	getUserRepository(): Repository<User, CreateUserInput, UpdateUserInput>
	getOrganizationRepository(): Repository<Organization, CreateOrganizationInput, UpdateOrganizationInput>
	getListingRepository(): Repository<Listing, CreateListingInput, UpdateListingInput>
	getAgentRepository(): Repository<Agent, CreateAgentInput, UpdateAgentInput>
	getLeadRepository(): Repository<Lead, CreateLeadInput, UpdateLeadInput>
	getSessionRepository(): Repository<Session, CreateSessionInput, UpdateSessionInput>
}

// Input Types for CRUD operations
export interface CreateUserInput {
	email: string
	name?: string
	passwordHash: string
	role: UserRole
	organizationId?: string
}

export interface UpdateUserInput {
	email?: string
	name?: string
	passwordHash?: string
	role?: UserRole
	organizationId?: string
}

export interface CreateOrganizationInput {
	name: string
	plan: 'free' | 'pro' | 'enterprise'
	settings: OrganizationSettings
}

export interface UpdateOrganizationInput {
	name?: string
	plan?: 'free' | 'pro' | 'enterprise'
	settings?: Partial<OrganizationSettings>
}

export interface CreateListingInput {
	organizationId: string
	title: string
	description: string
	price: number
	beds: number
	baths: number
	sqft?: number
	address: string
	city: string
	state: string
	zipCode: string
	country: string
	latitude?: number
	longitude?: number
	status: 'draft' | 'active' | 'pending' | 'sold' | 'archived'
	type: 'house' | 'apartment' | 'condo' | 'townhouse' | 'land' | 'commercial'
	images: string[]
	features: string[]
	agentId?: string
	seo?: SEOOverride
}

export interface UpdateListingInput {
	title?: string
	description?: string
	price?: number
	beds?: number
	baths?: number
	sqft?: number
	address?: string
	city?: string
	state?: string
	zipCode?: string
	country?: string
	latitude?: number
	longitude?: number
	status?: 'draft' | 'active' | 'pending' | 'sold' | 'archived'
	type?: 'house' | 'apartment' | 'condo' | 'townhouse' | 'land' | 'commercial'
	images?: string[]
	features?: string[]
	agentId?: string
	seo?: SEOOverride
}

export interface CreateAgentInput {
	userId?: string
	name: string
	email: string
	phone?: string
	bio?: string
	avatar?: string
	licenseNumber?: string
	organizationId: string
	specialties: string[]
	languages: string[]
	isActive: boolean
}

export interface UpdateAgentInput {
	userId?: string
	name?: string
	email?: string
	phone?: string
	bio?: string
	avatar?: string
	licenseNumber?: string
	specialties?: string[]
	languages?: string[]
	isActive?: boolean
}

export interface CreateLeadInput {
	name: string
	email: string
	phone?: string
	message?: string
	source: 'website' | 'referral' | 'social' | 'advertising' | 'other'
	status: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost'
	listingId?: string
	agentId?: string
	organizationId: string
	aiScore?: number
	aiInsights?: string
}

export interface UpdateLeadInput {
	name?: string
	email?: string
	phone?: string
	message?: string
	source?: 'website' | 'referral' | 'social' | 'advertising' | 'other'
	status?: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost'
	listingId?: string
	agentId?: string
	aiScore?: number
	aiInsights?: string
}

export interface Session {
	id: string
	token: string
	userId: string
	expiresAt: Date
	createdAt: Date
	updatedAt: Date
}

export interface CreateSessionInput {
	token: string
	userId: string
	expiresAt: Date
}

export interface UpdateSessionInput {
	token?: string
	expiresAt?: Date
}
