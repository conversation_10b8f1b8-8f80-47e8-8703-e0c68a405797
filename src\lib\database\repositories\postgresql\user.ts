import type { 
  Repository, 
  User, 
  CreateUserInput, 
  UpdateUserInput, 
  QueryOptions,
  CreateOptions,
  UpdateOptions,
  DeleteOptions
} from '@/lib/types'
import { generateId } from '@/lib/utils'

export class PostgreSQLUserRepository implements Repository<User, CreateUserInput, UpdateUserInput> {
  constructor(private prisma: any) {}

  async findById(id: string, options?: QueryOptions): Promise<User | null> {
    try {
      const include = this.buildInclude(options?.include)
      
      const user = await this.prisma.user.findUnique({
        where: { id },
        include,
        ...(options?.orderBy && { orderBy: this.buildOrderBy(options.orderBy) })
      })

      return user ? this.mapToUser(user) : null
    } catch (error) {
      throw new Error(`Failed to find user by ID: ${error}`)
    }
  }

  async findMany(filters?: Partial<User>, options?: QueryOptions): Promise<User[]> {
    try {
      const where = this.buildWhereClause(filters)
      const include = this.buildInclude(options?.include)
      
      const users = await this.prisma.user.findMany({
        where,
        include,
        ...(options?.limit && { take: options.limit }),
        ...(options?.offset && { skip: options.offset }),
        ...(options?.orderBy && { orderBy: this.buildOrderBy(options.orderBy) })
      })

      return users.map(this.mapToUser)
    } catch (error) {
      throw new Error(`Failed to find users: ${error}`)
    }
  }

  async create(data: CreateUserInput, options?: CreateOptions): Promise<User> {
    try {
      const include = this.buildInclude(options?.include)
      
      const user = await this.prisma.user.create({
        data: {
          id: generateId(),
          ...data,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        include
      })

      return this.mapToUser(user)
    } catch (error) {
      throw new Error(`Failed to create user: ${error}`)
    }
  }

  async update(id: string, data: Partial<UpdateUserInput>, options?: UpdateOptions): Promise<User> {
    try {
      const include = this.buildInclude(options?.include)
      
      const user = await this.prisma.user.update({
        where: { id },
        data: {
          ...data,
          updatedAt: new Date()
        },
        include
      })

      return this.mapToUser(user)
    } catch (error) {
      if (options?.upsert) {
        // If upsert is enabled and user doesn't exist, create it
        return this.create(data as CreateUserInput, options)
      }
      throw new Error(`Failed to update user: ${error}`)
    }
  }

  async delete(id: string, options?: DeleteOptions): Promise<boolean> {
    try {
      if (options?.cascade) {
        // Delete related records first
        await this.prisma.session.deleteMany({
          where: { userId: id }
        })
        
        await this.prisma.agent.deleteMany({
          where: { userId: id }
        })
      }

      await this.prisma.user.delete({
        where: { id }
      })

      return true
    } catch (error) {
      throw new Error(`Failed to delete user: ${error}`)
    }
  }

  async count(filters?: Partial<User>): Promise<number> {
    try {
      const where = this.buildWhereClause(filters)
      
      return await this.prisma.user.count({ where })
    } catch (error) {
      throw new Error(`Failed to count users: ${error}`)
    }
  }

  // Helper methods
  private buildWhereClause(filters?: Partial<User>): any {
    if (!filters) return {}

    const where: any = {}

    if (filters.id) where.id = filters.id
    if (filters.email) where.email = { contains: filters.email, mode: 'insensitive' }
    if (filters.name) where.name = { contains: filters.name, mode: 'insensitive' }
    if (filters.role) where.role = filters.role
    if (filters.organizationId) where.organizationId = filters.organizationId
    if (filters.createdAt) where.createdAt = { gte: filters.createdAt }
    if (filters.updatedAt) where.updatedAt = { gte: filters.updatedAt }

    return where
  }

  private buildInclude(include?: string[]): any {
    if (!include || include.length === 0) return undefined

    const includeObj: any = {}

    if (include.includes('organization')) {
      includeObj.organization = true
    }

    if (include.includes('agents')) {
      includeObj.agents = true
    }

    if (include.includes('sessions')) {
      includeObj.sessions = true
    }

    return includeObj
  }

  private buildOrderBy(orderBy: Array<{ field: string; direction: 'asc' | 'desc' }>): any {
    return orderBy.map(order => ({
      [order.field]: order.direction
    }))
  }

  private mapToUser(prismaUser: any): User {
    return {
      id: prismaUser.id,
      email: prismaUser.email,
      name: prismaUser.name,
      role: prismaUser.role,
      organizationId: prismaUser.organizationId,
      createdAt: prismaUser.createdAt,
      updatedAt: prismaUser.updatedAt
    }
  }

  // Additional user-specific methods
  async findByEmail(email: string): Promise<User | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { email }
      })

      return user ? this.mapToUser(user) : null
    } catch (error) {
      throw new Error(`Failed to find user by email: ${error}`)
    }
  }

  async findByOrganization(organizationId: string, options?: QueryOptions): Promise<User[]> {
    return this.findMany({ organizationId }, options)
  }

  async updatePassword(id: string, passwordHash: string): Promise<User> {
    return this.update(id, { passwordHash })
  }

  async activateUser(id: string): Promise<User> {
    // Assuming there's an isActive field
    return this.update(id, { isActive: true } as any)
  }

  async deactivateUser(id: string): Promise<User> {
    // Assuming there's an isActive field
    return this.update(id, { isActive: false } as any)
  }
}
