import { getDatabaseAdapter } from './adapter'
import type { 
  User, 
  Organization, 
  Listing, 
  Agent, 
  Lead, 
  Session,
  CreateUserInput,
  UpdateUserInput,
  CreateOrganizationInput,
  UpdateOrganizationInput,
  CreateListingInput,
  UpdateListingInput,
  CreateAgentInput,
  UpdateAgentInput,
  CreateLeadInput,
  UpdateLeadInput,
  CreateSessionInput,
  UpdateSessionInput,
  QueryOptions
} from '@/lib/types'
import { generateId, generateSecureToken } from '@/lib/utils'
import bcrypt from 'bcryptjs'

/**
 * Database Service Layer
 * Provides high-level database operations with business logic
 * Implements security, validation, and organization-scoped access
 */
export class DatabaseService {
  private adapter = getDatabaseAdapter()

  // User Operations
  async createUser(data: CreateUserInput): Promise<User> {
    const userRepo = this.adapter.getUserRepository()
    
    // Check if user already exists
    const existingUser = await userRepo.findMany({ email: data.email })
    if (existingUser.length > 0) {
      throw new Error('User with this email already exists')
    }

    return userRepo.create(data)
  }

  async getUserById(id: string, organizationId?: string): Promise<User | null> {
    const userRepo = this.adapter.getUserRepository()
    const user = await userRepo.findById(id)
    
    // Enforce organization-scoped access
    if (user && organizationId && user.organizationId !== organizationId) {
      return null // User not in the same organization
    }
    
    return user
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const userRepo = this.adapter.getUserRepository()
    const users = await userRepo.findMany({ email })
    return users.length > 0 ? users[0] : null
  }

  async updateUser(id: string, data: Partial<UpdateUserInput>, organizationId?: string): Promise<User> {
    const userRepo = this.adapter.getUserRepository()
    
    // Verify user exists and belongs to organization
    const existingUser = await this.getUserById(id, organizationId)
    if (!existingUser) {
      throw new Error('User not found or access denied')
    }

    return userRepo.update(id, data)
  }

  async deleteUser(id: string, organizationId?: string): Promise<boolean> {
    const userRepo = this.adapter.getUserRepository()
    
    // Verify user exists and belongs to organization
    const existingUser = await this.getUserById(id, organizationId)
    if (!existingUser) {
      throw new Error('User not found or access denied')
    }

    return userRepo.delete(id, { cascade: true })
  }

  // Session Operations (Secure Session Management)
  async createSession(userId: string): Promise<Session> {
    const sessionRepo = this.adapter.getSessionRepository()
    
    // Generate secure session token
    const token = generateSecureToken(64)
    const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days

    return sessionRepo.create({
      token,
      userId,
      expiresAt
    })
  }

  async getSessionByToken(token: string): Promise<Session | null> {
    const sessionRepo = this.adapter.getSessionRepository()
    const sessions = await sessionRepo.findMany({ token })
    
    if (sessions.length === 0) return null
    
    const session = sessions[0]
    
    // Check if session is expired
    if (session.expiresAt < new Date()) {
      await this.deleteSession(session.id)
      return null
    }
    
    return session
  }

  async deleteSession(sessionId: string): Promise<boolean> {
    const sessionRepo = this.adapter.getSessionRepository()
    return sessionRepo.delete(sessionId)
  }

  async deleteUserSessions(userId: string): Promise<void> {
    const sessionRepo = this.adapter.getSessionRepository()
    const sessions = await sessionRepo.findMany({ userId })
    
    await Promise.all(
      sessions.map(session => sessionRepo.delete(session.id))
    )
  }

  // Organization Operations
  async createOrganization(data: CreateOrganizationInput): Promise<Organization> {
    const orgRepo = this.adapter.getOrganizationRepository()
    return orgRepo.create(data)
  }

  async getOrganizationById(id: string): Promise<Organization | null> {
    const orgRepo = this.adapter.getOrganizationRepository()
    return orgRepo.findById(id)
  }

  async updateOrganization(id: string, data: Partial<UpdateOrganizationInput>): Promise<Organization> {
    const orgRepo = this.adapter.getOrganizationRepository()
    return orgRepo.update(id, data)
  }

  // Listing Operations (Organization-scoped)
  async createListing(data: CreateListingInput, organizationId: string): Promise<Listing> {
    // Enforce organization scope
    if (data.organizationId !== organizationId) {
      throw new Error('Cannot create listing for different organization')
    }

    const listingRepo = this.adapter.getListingRepository()
    return listingRepo.create(data)
  }

  async getListingById(id: string, organizationId?: string): Promise<Listing | null> {
    const listingRepo = this.adapter.getListingRepository()
    const listing = await listingRepo.findById(id)
    
    // Enforce organization-scoped access
    if (listing && organizationId && listing.organizationId !== organizationId) {
      return null
    }
    
    return listing
  }

  async getListingsByOrganization(organizationId: string, options?: QueryOptions): Promise<Listing[]> {
    const listingRepo = this.adapter.getListingRepository()
    return listingRepo.findMany({ organizationId }, options)
  }

  async updateListing(id: string, data: Partial<UpdateListingInput>, organizationId: string): Promise<Listing> {
    const listingRepo = this.adapter.getListingRepository()
    
    // Verify listing exists and belongs to organization
    const existingListing = await this.getListingById(id, organizationId)
    if (!existingListing) {
      throw new Error('Listing not found or access denied')
    }

    return listingRepo.update(id, data)
  }

  async deleteListing(id: string, organizationId: string): Promise<boolean> {
    const listingRepo = this.adapter.getListingRepository()
    
    // Verify listing exists and belongs to organization
    const existingListing = await this.getListingById(id, organizationId)
    if (!existingListing) {
      throw new Error('Listing not found or access denied')
    }

    return listingRepo.delete(id)
  }

  // Agent Operations (Organization-scoped)
  async createAgent(data: CreateAgentInput, organizationId: string): Promise<Agent> {
    // Enforce organization scope
    if (data.organizationId !== organizationId) {
      throw new Error('Cannot create agent for different organization')
    }

    const agentRepo = this.adapter.getAgentRepository()
    return agentRepo.create(data)
  }

  async getAgentsByOrganization(organizationId: string, options?: QueryOptions): Promise<Agent[]> {
    const agentRepo = this.adapter.getAgentRepository()
    return agentRepo.findMany({ organizationId }, options)
  }

  // Lead Operations (Organization-scoped)
  async createLead(data: CreateLeadInput, organizationId: string): Promise<Lead> {
    // Enforce organization scope
    if (data.organizationId !== organizationId) {
      throw new Error('Cannot create lead for different organization')
    }

    const leadRepo = this.adapter.getLeadRepository()
    return leadRepo.create(data)
  }

  async getLeadsByOrganization(organizationId: string, options?: QueryOptions): Promise<Lead[]> {
    const leadRepo = this.adapter.getLeadRepository()
    return leadRepo.findMany({ organizationId }, options)
  }

  // Authentication Helpers
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12)
  }

  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }

  // Transaction Support
  async transaction<T>(fn: () => Promise<T>): Promise<T> {
    return this.adapter.transaction(fn)
  }

  // Database Connection Management
  async connect(): Promise<void> {
    return this.adapter.connect()
  }

  async disconnect(): Promise<void> {
    return this.adapter.disconnect()
  }
}

// Singleton instance
let databaseService: DatabaseService | null = null

export function getDatabaseService(): DatabaseService {
  if (!databaseService) {
    databaseService = new DatabaseService()
  }
  return databaseService
}
