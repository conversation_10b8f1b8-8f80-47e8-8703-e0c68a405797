# Database Abstraction Layer Migration Strategy

## Overview

This document outlines the strategy for migrating the RealHub platform from mock data to a production-ready database abstraction layer that supports both PostgreSQL and MongoDB.

## Current State Analysis

### Issues Identified
1. **Mock Data Duplication**: Identical mock arrays across 4+ files
2. **No Persistence**: Data lost on server restart
3. **Security Vulnerabilities**: Weak ID generation, no session persistence
4. **Type Inconsistencies**: Mock data doesn't match TypeScript interfaces
5. **No RBAC Enforcement**: Missing organization-scoped access control

### Dependencies Present
- `@prisma/client: ^6.12.0`
- `prisma: ^6.12.0`
- `@auth/prisma-adapter: ^2.10.0`

## Migration Strategy

### Phase 1: Database Schema Setup (Immediate)

#### PostgreSQL Setup
1. **Prisma Schema**: Created `prisma/schema.prisma` with complete entity definitions
2. **Environment Variables**: Add to `.env`:
   ```env
   DATABASE_TYPE=postgresql
   DATABASE_URL="postgresql://username:password@localhost:5432/realhub"
   ```
3. **Initialize Database**:
   ```bash
   npx prisma generate
   npx prisma db push
   ```

#### MongoDB Setup (Alternative)
1. **Environment Variables**: Add to `.env`:
   ```env
   DATABASE_TYPE=mongodb
   DATABASE_URL="mongodb://localhost:27017/realhub"
   ```
2. **Install MongoDB Driver**:
   ```bash
   npm install mongodb
   ```

### Phase 2: Database Abstraction Layer (Completed)

#### Core Components Created
1. **`src/lib/database/adapter.ts`**: Main abstraction interface
2. **`src/lib/database/repositories/postgresql/user.ts`**: PostgreSQL implementation
3. **`src/lib/database/repositories/mongodb/user.ts`**: MongoDB implementation
4. **`src/lib/database/service.ts`**: High-level business logic layer

#### Key Features
- **Repository Pattern**: Consistent interface for both databases
- **Type Safety**: Full TypeScript support maintained
- **Organization-Scoped Access**: Multi-tenant security built-in
- **Transaction Support**: Database-specific transaction handling
- **Connection Management**: Singleton pattern with proper cleanup

### Phase 3: Replace Mock Data (Next Steps)

#### 3.1 Update Authentication Routes

**File**: `src/app/api/v1/auth/signup/route.ts`
```typescript
// Replace mock operations with:
import { getDatabaseService } from '@/lib/database/service'

const db = getDatabaseService()
await db.connect()

// Replace user creation
const user = await db.createUser({
  email: validatedData.email,
  name: validatedData.name,
  passwordHash: await db.hashPassword(validatedData.password),
  role: validatedData.role,
  organizationId
})
```

**File**: `src/app/api/v1/auth/signin/route.ts`
```typescript
// Replace user lookup and session creation
const user = await db.getUserByEmail(validatedData.email)
const isValid = await db.verifyPassword(validatedData.password, user.passwordHash)
const session = await db.createSession(user.id)
```

**File**: `src/app/api/v1/auth/me/route.ts`
```typescript
// Replace session validation
const session = await db.getSessionByToken(sessionToken)
const user = await db.getUserById(session.userId)
```

#### 3.2 Update Server Actions

**File**: `src/app/(auth)/_actions/auth.ts`
```typescript
// Replace all mock operations with database service calls
import { getDatabaseService } from '@/lib/database/service'

export async function signUpAction(formData: FormData): Promise<APIResponse> {
  const db = getDatabaseService()
  
  try {
    const result = await db.transaction(async () => {
      // Create organization if needed
      let organizationId: string | undefined
      if (validatedData.organizationName) {
        const org = await db.createOrganization({
          name: validatedData.organizationName,
          plan: 'free',
          settings: {
            allowPublicListings: true,
            requireApproval: false,
            defaultLanguage: 'en',
            timezone: 'UTC'
          }
        })
        organizationId = org.id
      }

      // Create user
      const user = await db.createUser({
        email: validatedData.email,
        name: validatedData.name,
        passwordHash: await db.hashPassword(validatedData.password),
        role: validatedData.role,
        organizationId
      })

      // Create session
      await db.createSession(user.id)
      
      return user
    })

    return { data: { user: result }, message: 'Account created successfully!' }
  } catch (error) {
    return { error: error.message }
  }
}
```

### Phase 4: Security Enhancements

#### 4.1 Secure Session Management
- **Database-backed sessions**: Sessions stored in database with expiration
- **Secure token generation**: Cryptographically secure random tokens
- **Session cleanup**: Automatic cleanup of expired sessions

#### 4.2 RBAC Implementation
- **Organization-scoped access**: All operations enforce organization boundaries
- **Role-based permissions**: Granular permission checking
- **Resource ownership**: Users can only access their organization's data

#### 4.3 Input Validation & Sanitization
- **Zod validation**: Consistent validation across all endpoints
- **SQL injection protection**: Parameterized queries via Prisma/MongoDB driver
- **XSS protection**: Input sanitization for user-generated content

### Phase 5: Testing Strategy

#### 5.1 Unit Tests
```typescript
// Example test for database service
describe('DatabaseService', () => {
  let db: DatabaseService
  
  beforeEach(async () => {
    db = getDatabaseService()
    await db.connect()
  })
  
  afterEach(async () => {
    await db.disconnect()
  })
  
  it('should create user with organization scope', async () => {
    const org = await db.createOrganization({
      name: 'Test Org',
      plan: 'free',
      settings: { /* ... */ }
    })
    
    const user = await db.createUser({
      email: '<EMAIL>',
      name: 'Test User',
      passwordHash: 'hashed_password',
      role: 'agent',
      organizationId: org.id
    })
    
    expect(user.organizationId).toBe(org.id)
  })
})
```

#### 5.2 Integration Tests
- **Database switching**: Test both PostgreSQL and MongoDB implementations
- **Transaction rollback**: Ensure data consistency
- **Performance testing**: Compare query performance between databases

### Phase 6: Deployment Considerations

#### 6.1 Environment Configuration
```env
# Production PostgreSQL
DATABASE_TYPE=postgresql
DATABASE_URL="***********************************/realhub"

# Production MongoDB
DATABASE_TYPE=mongodb
DATABASE_URL="**********************************************"
```

#### 6.2 Migration Scripts
```typescript
// scripts/migrate-to-database.ts
import { getDatabaseService } from '@/lib/database/service'

async function migrateExistingData() {
  const db = getDatabaseService()
  await db.connect()
  
  // Migrate any existing mock data to database
  // This would be run once during deployment
}
```

## Implementation Checklist

### Immediate Actions Required

- [ ] **Set up environment variables** for database connection
- [ ] **Run Prisma migrations** to create database schema
- [ ] **Replace mock data in auth routes** with database service calls
- [ ] **Update Server Actions** to use database service
- [ ] **Add error handling** for database connection failures
- [ ] **Implement session cleanup** job for expired sessions
- [ ] **Add database health checks** for monitoring
- [ ] **Create migration scripts** for production deployment

### Testing Requirements

- [ ] **Unit tests** for all repository implementations
- [ ] **Integration tests** for database switching
- [ ] **Performance benchmarks** for both database types
- [ ] **Security testing** for RBAC implementation
- [ ] **Load testing** with real database connections

### Documentation Updates

- [ ] **API documentation** reflecting real database operations
- [ ] **Deployment guide** for both database types
- [ ] **Performance tuning** recommendations
- [ ] **Backup and recovery** procedures

## Benefits of This Approach

1. **Type Safety**: Full TypeScript support maintained throughout
2. **Database Flexibility**: Easy switching between PostgreSQL and MongoDB
3. **Performance**: Database-specific optimizations possible
4. **Security**: Organization-scoped access and secure session management
5. **Maintainability**: Clean separation of concerns with repository pattern
6. **Testability**: Easy to mock repositories for unit testing
7. **Scalability**: Connection pooling and transaction support built-in

## Risk Mitigation

1. **Gradual Migration**: Replace mock data incrementally
2. **Rollback Plan**: Keep mock data as fallback during transition
3. **Monitoring**: Add comprehensive logging and health checks
4. **Testing**: Extensive testing before production deployment
5. **Documentation**: Clear migration procedures and troubleshooting guides
