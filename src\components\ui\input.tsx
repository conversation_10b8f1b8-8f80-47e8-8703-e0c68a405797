import * as React from 'react'
import { cn } from '@/lib/utils'

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
	/** Optional error message to display */
	error?: string
	/** Optional helper text to display below the input */
	helperText?: string
	/** Input variant for different styling */
	variant?: 'default' | 'search' | 'currency'
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
	({ className, type, error, helperText, variant = 'default', ...props }, ref) => {
		const variantStyles = {
			default: '',
			search: 'pl-10', // Space for search icon
			currency: 'pl-8' // Space for currency symbol
		}

		return (
			<div className="w-full">
				<input
					type={type}
					className={cn(
						'flex h-10 w-full rounded-lg border bg-card px-3 py-2 text-sm text-text placeholder:text-secondary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
						error ? 'border-red-500 focus-visible:ring-red-500' : 'border-border',
						variantStyles[variant],
						className
					)}
					ref={ref}
					{...props}
				/>
				{error && <p className="mt-1 text-sm text-red-500">{error}</p>}
				{helperText && !error && <p className="mt-1 text-sm text-secondary">{helperText}</p>}
			</div>
		)
	}
)
Input.displayName = 'Input'

export { Input }
