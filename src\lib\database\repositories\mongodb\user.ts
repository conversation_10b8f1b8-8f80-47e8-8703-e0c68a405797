import type { 
  Repository, 
  User, 
  CreateUserInput, 
  UpdateUserInput, 
  QueryOptions,
  CreateOptions,
  UpdateOptions,
  DeleteOptions
} from '@/lib/types'
import { generateId } from '@/lib/utils'

export class MongoDBUserRepository implements Repository<User, CreateUserInput, UpdateUserInput> {
  private collection: any

  constructor(private db: any) {
    this.collection = db.collection('users')
    
    // Create indexes for better performance
    this.createIndexes()
  }

  private async createIndexes(): Promise<void> {
    try {
      await this.collection.createIndex({ email: 1 }, { unique: true })
      await this.collection.createIndex({ organizationId: 1 })
      await this.collection.createIndex({ role: 1 })
      await this.collection.createIndex({ createdAt: 1 })
    } catch (error) {
      // Indexes might already exist, ignore errors
    }
  }

  async findById(id: string, options?: QueryOptions): Promise<User | null> {
    try {
      const pipeline = this.buildAggregationPipeline({ id }, options)
      const result = await this.collection.aggregate(pipeline).toArray()
      
      return result.length > 0 ? this.mapToUser(result[0]) : null
    } catch (error) {
      throw new Error(`Failed to find user by ID: ${error}`)
    }
  }

  async findMany(filters?: Partial<User>, options?: QueryOptions): Promise<User[]> {
    try {
      const pipeline = this.buildAggregationPipeline(filters, options)
      const result = await this.collection.aggregate(pipeline).toArray()
      
      return result.map(this.mapToUser)
    } catch (error) {
      throw new Error(`Failed to find users: ${error}`)
    }
  }

  async create(data: CreateUserInput, options?: CreateOptions): Promise<User> {
    try {
      const now = new Date()
      const userData = {
        _id: generateId(),
        ...data,
        createdAt: now,
        updatedAt: now
      }

      await this.collection.insertOne(userData)
      
      // If include options are specified, fetch with relations
      if (options?.include && options.include.length > 0) {
        return await this.findById(userData._id, { include: options.include }) as User
      }

      return this.mapToUser(userData)
    } catch (error) {
      if (error.code === 11000) {
        throw new Error('User with this email already exists')
      }
      throw new Error(`Failed to create user: ${error}`)
    }
  }

  async update(id: string, data: Partial<UpdateUserInput>, options?: UpdateOptions): Promise<User> {
    try {
      const updateData = {
        ...data,
        updatedAt: new Date()
      }

      const result = await this.collection.findOneAndUpdate(
        { _id: id },
        { $set: updateData },
        { 
          returnDocument: 'after',
          upsert: options?.upsert || false
        }
      )

      if (!result.value) {
        throw new Error('User not found')
      }

      // If include options are specified, fetch with relations
      if (options?.include && options.include.length > 0) {
        return await this.findById(id, { include: options.include }) as User
      }

      return this.mapToUser(result.value)
    } catch (error) {
      throw new Error(`Failed to update user: ${error}`)
    }
  }

  async delete(id: string, options?: DeleteOptions): Promise<boolean> {
    try {
      if (options?.cascade) {
        // Delete related records first
        await this.db.collection('sessions').deleteMany({ userId: id })
        await this.db.collection('agents').deleteMany({ userId: id })
      }

      const result = await this.collection.deleteOne({ _id: id })
      return result.deletedCount > 0
    } catch (error) {
      throw new Error(`Failed to delete user: ${error}`)
    }
  }

  async count(filters?: Partial<User>): Promise<number> {
    try {
      const query = this.buildQuery(filters)
      return await this.collection.countDocuments(query)
    } catch (error) {
      throw new Error(`Failed to count users: ${error}`)
    }
  }

  // Helper methods
  private buildQuery(filters?: Partial<User>): any {
    if (!filters) return {}

    const query: any = {}

    if (filters.id) query._id = filters.id
    if (filters.email) query.email = new RegExp(filters.email, 'i')
    if (filters.name) query.name = new RegExp(filters.name, 'i')
    if (filters.role) query.role = filters.role
    if (filters.organizationId) query.organizationId = filters.organizationId
    if (filters.createdAt) query.createdAt = { $gte: filters.createdAt }
    if (filters.updatedAt) query.updatedAt = { $gte: filters.updatedAt }

    return query
  }

  private buildAggregationPipeline(filters?: Partial<User>, options?: QueryOptions): any[] {
    const pipeline: any[] = []

    // Match stage
    const matchQuery = this.buildQuery(filters)
    if (Object.keys(matchQuery).length > 0) {
      pipeline.push({ $match: matchQuery })
    }

    // Lookup stages for relations
    if (options?.include) {
      if (options.include.includes('organization')) {
        pipeline.push({
          $lookup: {
            from: 'organizations',
            localField: 'organizationId',
            foreignField: '_id',
            as: 'organization'
          }
        })
        pipeline.push({
          $unwind: {
            path: '$organization',
            preserveNullAndEmptyArrays: true
          }
        })
      }

      if (options.include.includes('agents')) {
        pipeline.push({
          $lookup: {
            from: 'agents',
            localField: '_id',
            foreignField: 'userId',
            as: 'agents'
          }
        })
      }

      if (options.include.includes('sessions')) {
        pipeline.push({
          $lookup: {
            from: 'sessions',
            localField: '_id',
            foreignField: 'userId',
            as: 'sessions'
          }
        })
      }
    }

    // Sort stage
    if (options?.orderBy && options.orderBy.length > 0) {
      const sort: any = {}
      options.orderBy.forEach(order => {
        sort[order.field === 'id' ? '_id' : order.field] = order.direction === 'asc' ? 1 : -1
      })
      pipeline.push({ $sort: sort })
    }

    // Pagination
    if (options?.offset) {
      pipeline.push({ $skip: options.offset })
    }
    if (options?.limit) {
      pipeline.push({ $limit: options.limit })
    }

    return pipeline
  }

  private mapToUser(mongoUser: any): User {
    return {
      id: mongoUser._id,
      email: mongoUser.email,
      name: mongoUser.name,
      role: mongoUser.role,
      organizationId: mongoUser.organizationId,
      createdAt: mongoUser.createdAt,
      updatedAt: mongoUser.updatedAt
    }
  }

  // Additional user-specific methods
  async findByEmail(email: string): Promise<User | null> {
    try {
      const user = await this.collection.findOne({ email })
      return user ? this.mapToUser(user) : null
    } catch (error) {
      throw new Error(`Failed to find user by email: ${error}`)
    }
  }

  async findByOrganization(organizationId: string, options?: QueryOptions): Promise<User[]> {
    return this.findMany({ organizationId }, options)
  }

  async updatePassword(id: string, passwordHash: string): Promise<User> {
    return this.update(id, { passwordHash })
  }

  async activateUser(id: string): Promise<User> {
    return this.update(id, { isActive: true } as any)
  }

  async deactivateUser(id: string): Promise<User> {
    return this.update(id, { isActive: false } as any)
  }
}
